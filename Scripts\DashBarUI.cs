using UnityEngine;
using UnityEngine.UI;
using EasyPeasyFirstPersonController;

public class DashBarUI : MonoBehaviour
{
    [<PERSON><PERSON>("Existing Dash Bar References")]
    [Tooltip("Assign your existing dash bar images from the hierarchy")]
    public Image[] existingDashBars = new Image[3];

    [Header("Dash Bar Settings")]
    [Tooltip("The sprite to use for active dash bars")]
    public Sprite dashBarSprite;

    [Tooltip("The sprite to use for inactive/empty dash bars")]
    public Sprite emptyDashBarSprite;

    [<PERSON><PERSON>("Animation Settings")]
    [Tooltip("Enable smooth scaling animation when dash is used/recharged")]
    public bool enableAnimation = true;

    [Tooltip("Scale multiplier for animation effect")]
    [Range(1.1f, 2f)]
    public float animationScale = 1.3f;

    [Tooltip("Duration of the scale animation")]
    [Range(0.1f, 1f)]
    public float animationDuration = 0.3f;

    // Private variables
    private FirstPersonController playerController;
    private Image[] dashBarImages;
    private RectTransform[] dashBarTransforms;
    private int lastDashCount = -1;
    private float[] animationTimers;
    private Vector3[] originalScales;
    
    void Start()
    {
        // Find the FirstPersonController
        playerController = FindFirstObjectByType<FirstPersonController>();
        if (playerController == null)
        {
            Debug.LogError("DashBarUI: Could not find FirstPersonController in the scene!");
            enabled = false;
            return;
        }

        // Validate existing dash bars
        if (existingDashBars == null || existingDashBars.Length != 3)
        {
            Debug.LogError("DashBarUI: Please assign exactly 3 existing dash bar images in the inspector!");
            enabled = false;
            return;
        }

        // Check if all dash bars are assigned
        for (int i = 0; i < 3; i++)
        {
            if (existingDashBars[i] == null)
            {
                Debug.LogError($"DashBarUI: Dash bar {i + 1} is not assigned in the inspector!");
                enabled = false;
                return;
            }
        }

        // Validate sprites
        if (dashBarSprite == null)
        {
            Debug.LogError("DashBarUI: Dash Bar Sprite is not assigned!");
            enabled = false;
            return;
        }

        // Use the same sprite for empty bars if not specified
        if (emptyDashBarSprite == null)
        {
            emptyDashBarSprite = dashBarSprite;
        }

        SetupExistingDashBars();
        UpdateDashBars();
    }
    
    void SetupExistingDashBars()
    {
        // Initialize arrays
        dashBarImages = new Image[3];
        dashBarTransforms = new RectTransform[3];
        animationTimers = new float[3];
        originalScales = new Vector3[3];

        for (int i = 0; i < 3; i++)
        {
            // Use the existing dash bar images
            dashBarImages[i] = existingDashBars[i];
            dashBarTransforms[i] = existingDashBars[i].GetComponent<RectTransform>();

            // Store original scale for animations
            originalScales[i] = dashBarTransforms[i].localScale;
            animationTimers[i] = 0f;

            // Set initial sprite
            dashBarImages[i].sprite = dashBarSprite;
        }
    }
    
    void Update()
    {
        if (playerController == null) return;
        
        UpdateDashBars();
        UpdateAnimations();
    }
    
    void UpdateDashBars()
    {
        int currentDashCount = playerController.CurrentDashCharges;
        
        // Check if dash count changed for animation trigger
        bool dashCountChanged = currentDashCount != lastDashCount;
        
        for (int i = 0; i < 3; i++)
        {
            if (dashBarImages[i] == null) continue;
            
            // Update sprite based on available dashes
            bool isActive = i < currentDashCount;
            dashBarImages[i].sprite = isActive ? dashBarSprite : emptyDashBarSprite;
            
            // Set alpha for visual feedback
            Color color = dashBarImages[i].color;
            color.a = isActive ? 1f : 0.5f;
            dashBarImages[i].color = color;
            
            // Trigger animation if dash count changed and this bar was affected
            if (enableAnimation && dashCountChanged)
            {
                if ((currentDashCount > lastDashCount && i == currentDashCount - 1) || // Dash recharged
                    (currentDashCount < lastDashCount && i == currentDashCount)) // Dash used
                {
                    animationTimers[i] = animationDuration;
                }
            }
        }
        
        lastDashCount = currentDashCount;
    }
    
    void UpdateAnimations()
    {
        if (!enableAnimation) return;
        
        for (int i = 0; i < 3; i++)
        {
            if (animationTimers[i] > 0f)
            {
                animationTimers[i] -= Time.deltaTime;
                
                // Calculate scale based on animation progress
                float progress = 1f - (animationTimers[i] / animationDuration);
                float scale = Mathf.Lerp(animationScale, 1f, progress);
                
                if (dashBarTransforms[i] != null)
                {
                    dashBarTransforms[i].localScale = originalScales[i] * scale;
                }
                
                // Reset scale when animation is complete
                if (animationTimers[i] <= 0f && dashBarTransforms[i] != null)
                {
                    dashBarTransforms[i].localScale = originalScales[i];
                }
            }
        }
    }
    
    // Public method to manually refresh the UI (useful for testing)
    public void RefreshDashBars()
    {
        if (playerController != null)
        {
            lastDashCount = -1; // Force update
            UpdateDashBars();
        }
    }
    
    // Public method to manually set dash bar sprites
    public void SetDashBarSprites(Sprite activeSprite, Sprite inactiveSprite = null)
    {
        dashBarSprite = activeSprite;
        emptyDashBarSprite = inactiveSprite ?? activeSprite;

        // Update all existing bars with new sprites
        if (dashBarImages != null)
        {
            for (int i = 0; i < 3; i++)
            {
                if (dashBarImages[i] != null)
                {
                    int currentDashCount = playerController != null ? playerController.CurrentDashCharges : 3;
                    bool isActive = i < currentDashCount;
                    dashBarImages[i].sprite = isActive ? dashBarSprite : emptyDashBarSprite;
                }
            }
        }
    }
}
